using System;
using System.Data;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using Npgsql;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.LoginForm;
using ProManage.Modules.Helpers;

namespace ProManage.Modules.Data.LoginForm
{
    /// <summary>
    /// User authentication and session management for LoginForm
    /// Handles user validation, password verification, and session management
    /// </summary>
    public static class LoginFormUserManager
    {
        #region User Authentication

        /// <summary>
        /// Validates user credentials against the database
        /// </summary>
        /// <param name="username">Username to validate</param>
        /// <param name="password">Password to validate</param>
        /// <returns>True if credentials are valid, false otherwise</returns>
        public static bool ValidateUserCredentials(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    Debug.WriteLine("LoginFormUserManager: Empty username or password provided");
                    return false;
                }

                using (var connection = DatabaseConnectionManager.GetConnection())
                {
                    connection.Open();

                    // Load the user validation query
                    var query = SQLQueryLoader.LoadNamedQuery(
                        "User",
                        "ValidateUser",
                        "VALIDATE_USER_CREDENTIALS"
                    );

                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@username", username.ToLower().Trim());
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var storedPasswordHash = reader["password_hash"]?.ToString();
                                var isActive = Convert.ToBoolean(reader["active"]);

                                if (!isActive)
                                {
                                    Debug.WriteLine($"LoginFormUserManager: User {username} is not active");
                                    return false;
                                }

                                // Verify password hash
                                return VerifyPassword(password, storedPasswordHash);
                            }
                        }
                    }
                }

                Debug.WriteLine($"LoginFormUserManager: User {username} not found in database");
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LoginFormUserManager: Error validating user credentials: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets user information by username
        /// </summary>
        /// <param name="username">Username to retrieve</param>
        /// <returns>LoginFormUserModel or null if not found</returns>
        public static LoginFormUserModel GetUserByUsername(string username)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username))
                    return null;

                using (var connection = DatabaseConnectionManager.GetConnection())
                {
                    connection.Open();

                    var query = SQLQueryLoader.LoadNamedQuery(
                        "User",
                        "GetUser",
                        "GET_USER_BY_USERNAME"
                    );

                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@username", username.ToLower().Trim());

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new LoginFormUserModel
                                {
                                    Id = Convert.ToInt32(reader["user_id"]),
                                    Username = reader["username"]?.ToString(),
                                    FullName = reader["full_name"]?.ToString(),
                                    Email = reader["email"]?.ToString(),
                                    IsActive = Convert.ToBoolean(reader["active"]),
                                    CreatedAt = reader["created_date"] as DateTime?
                                };
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LoginFormUserManager: Error getting user by username: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Password Management

        /// <summary>
        /// Hashes a password using SHA256
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>Hashed password</returns>
        public static string HashPassword(string password)
        {
            try
            {
                if (string.IsNullOrEmpty(password))
                    return string.Empty;

                using (var sha256 = SHA256.Create())
                {
                    var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                    return Convert.ToBase64String(hashedBytes);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LoginFormUserManager: Error hashing password: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Verifies a password against a stored hash
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="storedHash">Stored password hash</param>
        /// <returns>True if password matches, false otherwise</returns>
        public static bool VerifyPassword(string password, string storedHash)
        {
            try
            {
                if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(storedHash))
                    return false;

                var passwordHash = HashPassword(password);
                return string.Equals(passwordHash, storedHash, StringComparison.Ordinal);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LoginFormUserManager: Error verifying password: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Session Management

        /// <summary>
        /// Records user login session
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="loginTime">Login timestamp</param>
        public static void RecordUserLogin(int userId, DateTime loginTime)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.GetConnection())
                {
                    connection.Open();

                    var query = SQLQueryLoader.LoadNamedQuery(
                        "User",
                        "UserManagement",
                        "RECORD_USER_LOGIN"
                    );

                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);
                        command.Parameters.AddWithValue("@login_time", loginTime);
                        command.Parameters.AddWithValue("@ip_address", GetClientIPAddress());

                        command.ExecuteNonQuery();
                    }
                }

                Debug.WriteLine($"LoginFormUserManager: Recorded login for user ID {userId}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LoginFormUserManager: Error recording user login: {ex.Message}");
            }
        }

        /// <summary>
        /// Records user logout session
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="logoutTime">Logout timestamp</param>
        public static void RecordUserLogout(int userId, DateTime logoutTime)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.GetConnection())
                {
                    connection.Open();

                    var query = SQLQueryLoader.LoadNamedQuery(
                        "User",
                        "UserManagement",
                        "RECORD_USER_LOGOUT"
                    );

                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@user_id", userId);
                        command.Parameters.AddWithValue("@logout_time", logoutTime);

                        command.ExecuteNonQuery();
                    }
                }

                Debug.WriteLine($"LoginFormUserManager: Recorded logout for user ID {userId}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"LoginFormUserManager: Error recording user logout: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the client IP address (placeholder implementation)
        /// </summary>
        /// <returns>Client IP address</returns>
        private static string GetClientIPAddress()
        {
            try
            {
                // For desktop applications, this would typically be the local machine IP
                return System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
                    .AddressList[0].ToString();
            }
            catch
            {
                return "127.0.0.1"; // Fallback to localhost
            }
        }

        /// <summary>
        /// Validates username format
        /// </summary>
        /// <param name="username">Username to validate</param>
        /// <returns>True if valid format, false otherwise</returns>
        public static bool IsValidUsernameFormat(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
                return false;

            // Username should be 3-50 characters, alphanumeric and underscore only
            if (username.Length < 3 || username.Length > 50)
                return false;

            foreach (char c in username)
            {
                if (!char.IsLetterOrDigit(c) && c != '_')
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Validates password strength
        /// </summary>
        /// <param name="password">Password to validate</param>
        /// <returns>True if password meets requirements, false otherwise</returns>
        public static bool IsValidPasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            // Password should be at least 6 characters
            if (password.Length < 6)
                return false;

            // For now, just check minimum length
            // Additional complexity requirements can be added here
            return true;
        }

        #endregion
    }
}
